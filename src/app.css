/* Import Tailwind CSS for compatibility */
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* Apple Design System - Zero border radius, flat design, calm colors */
@import './lib/design-system/tokens.css';
@import './lib/design-system/base.css';

/* Override Tailwind with Apple Design System principles */
:root {
  /* Brand colors for existing components */
  --color-primary: #2563eb;
  --color-accent: #3b82f6;
  --color-warning: #f59e0b;
  --color-bg-soft: #f5f5f5;
}

/* Global Apple Design System overrides */
* {
  border-radius: 0 !important; /* Zero border radius everywhere */
}

/* Base body styles */
body {
  background-color: var(--color-gray-50);
  color: var(--color-gray-900);
  font-family: var(--font-family-sans);
  line-height: var(--leading-normal);
}

/* Apple Design System Global Styles */
.apple-page {
  min-height: 100vh;
  background-color: var(--color-gray-50);
  font-family: var(--font-family-sans);
}

.apple-container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.apple-section {
  padding: var(--space-16) 0;
}

.apple-header {
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
}

.apple-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
}

.apple-main {
  flex: 1;
  padding: var(--space-8) 0;
}

.apple-footer {
  background-color: var(--color-white);
  border-top: 1px solid var(--color-gray-200);
  padding: var(--space-8) 0;
  margin-top: auto;
}

/* Typography classes */
.apple-heading-1 {
  font-size: var(--text-5xl);
  font-weight: var(--font-light);
  line-height: var(--leading-tight);
  color: var(--color-gray-900);
  margin-bottom: var(--space-6);
}

.apple-heading-2 {
  font-size: var(--text-4xl);
  font-weight: var(--font-light);
  line-height: var(--leading-tight);
  color: var(--color-gray-900);
  margin-bottom: var(--space-4);
}

.apple-heading-3 {
  font-size: var(--text-3xl);
  font-weight: var(--font-light);
  line-height: var(--leading-snug);
  color: var(--color-gray-900);
  margin-bottom: var(--space-4);
}

.apple-heading-4 {
  font-size: var(--text-2xl);
  font-weight: var(--font-normal);
  line-height: var(--leading-snug);
  color: var(--color-gray-900);
  margin-bottom: var(--space-3);
}

.apple-body {
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-relaxed);
  color: var(--color-gray-700);
}

.apple-caption {
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--color-gray-500);
}

/* Animation utilities */
@keyframes apple-fade-in {
  from {
    opacity: 0;
    transform: translateY(var(--space-2));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes apple-slide-in {
  from {
    opacity: 0;
    transform: translateX(-var(--space-4));
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.apple-animate-fade-in {
  animation: apple-fade-in 0.6s ease-out;
}

.apple-animate-slide-in {
  animation: apple-slide-in 0.4s ease-out;
}

/* Compatibility styles for existing layout */
.gradient-bg {
  background: linear-gradient(to bottom right, var(--color-gray-50), var(--color-gray-100));
}

.glassmorphic {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
}

.card-glass {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-lg);
  padding: var(--space-6);
}

/* Button compatibility */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  border-radius: 0 !important;
}

.btn-primary {
  background-color: var(--color-blue-600);
  color: var(--color-white);
  border-color: var(--color-blue-600);
}

.btn-primary:hover {
  background-color: var(--color-blue-700);
  border-color: var(--color-blue-700);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
}

.btn-outline:hover {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
}

/* Text color utilities */
.text-primary {
  color: var(--color-blue-600);
}

/* Float animation compatibility */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-element {
  animation: float 6s ease-in-out infinite;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .apple-container {
    padding: 0 var(--space-4);
  }

  .apple-section {
    padding: var(--space-12) 0;
  }

  .apple-heading-1 {
    font-size: var(--text-4xl);
  }

  .apple-heading-2 {
    font-size: var(--text-3xl);
  }
}