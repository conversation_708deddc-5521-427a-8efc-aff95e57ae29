/* Apple Design System - Zero border radius, flat design, calm colors */
@import './lib/design-system/tokens.css';
@import './lib/design-system/base.css';


/* Apple Design System Global Styles */
.apple-page {
  min-height: 100vh;
  background-color: var(--color-gray-50);
  font-family: var(--font-family-sans);
}

.apple-container {
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.apple-section {
  padding: var(--space-16) 0;
}

.apple-header {
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
}

.apple-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
}

.apple-main {
  flex: 1;
  padding: var(--space-8) 0;
}

.apple-footer {
  background-color: var(--color-white);
  border-top: 1px solid var(--color-gray-200);
  padding: var(--space-8) 0;
  margin-top: auto;
}

/* Typography classes */
.apple-heading-1 {
  font-size: var(--text-5xl);
  font-weight: var(--font-light);
  line-height: var(--leading-tight);
  color: var(--color-gray-900);
  margin-bottom: var(--space-6);
}

.apple-heading-2 {
  font-size: var(--text-4xl);
  font-weight: var(--font-light);
  line-height: var(--leading-tight);
  color: var(--color-gray-900);
  margin-bottom: var(--space-4);
}

.apple-heading-3 {
  font-size: var(--text-3xl);
  font-weight: var(--font-light);
  line-height: var(--leading-snug);
  color: var(--color-gray-900);
  margin-bottom: var(--space-4);
}

.apple-heading-4 {
  font-size: var(--text-2xl);
  font-weight: var(--font-normal);
  line-height: var(--leading-snug);
  color: var(--color-gray-900);
  margin-bottom: var(--space-3);
}

.apple-body {
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-relaxed);
  color: var(--color-gray-700);
}

.apple-caption {
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--color-gray-500);
}

/* Animation utilities */
@keyframes apple-fade-in {
  from {
    opacity: 0;
    transform: translateY(var(--space-2));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes apple-slide-in {
  from {
    opacity: 0;
    transform: translateX(-var(--space-4));
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.apple-animate-fade-in {
  animation: apple-fade-in 0.6s ease-out;
}

.apple-animate-slide-in {
  animation: apple-slide-in 0.4s ease-out;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .apple-container {
    padding: 0 var(--space-4);
  }

  .apple-section {
    padding: var(--space-12) 0;
  }

  .apple-heading-1 {
    font-size: var(--text-4xl);
  }

  .apple-heading-2 {
    font-size: var(--text-3xl);
  }
}