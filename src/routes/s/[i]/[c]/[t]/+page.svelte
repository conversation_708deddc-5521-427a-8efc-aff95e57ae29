<script lang="ts">
  import { subjects } from '$lib/constants';
  import type { Score } from '$lib/types';
  export let data: {
    scores: Score[];
    subjects: typeof subjects;
    admin: boolean;
    i: string;
    c: string;
    t: string;
  };
</script>

<div class="flex justify-between items-center mb-6">
  <h1 class="text-3xl font-bold">Midterm Scores</h1>
  {#if data.admin}
    <a
      href="/s/{data.i}/{data.c}/{data.t}/e"
      class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors duration-200"
    >
      Edit Scores
    </a>
  {/if}
</div>

<table class="w-full border-collapse border border-gray-300">
  <thead class="bg-gray-50">
    <tr>
      <th class="border border-gray-300 px-4 py-2 text-left font-semibold">Subject</th>
      <th class="border border-gray-300 px-4 py-2 text-left font-semibold">Score</th>
    </tr>
  </thead>
  <tbody>
    {#each data.scores as score}
      <tr class="hover:bg-gray-50">
        <td class="border border-gray-300 px-4 py-2">{subjects[score.j as keyof typeof subjects]}</td>
        <td class="border border-gray-300 px-4 py-2 font-mono">{score.e || score[1] || score[2] || score[3] || score.p || '-'}</td>
      </tr>
    {/each}
  </tbody>
</table>

{#if data.scores.length === 0}
  <div class="text-center py-8 text-gray-500">
    <p>No scores available for this term.</p>
    {#if data.admin}
      <p class="mt-2">Click "Edit Scores" to add scores.</p>
    {/if}
  </div>
{/if}