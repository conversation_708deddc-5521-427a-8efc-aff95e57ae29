<script lang="ts">
  import { subjects } from '$lib/constants';
  import type { Score } from '$lib/types';
  import { onMount } from 'svelte';
  import { Button, Card, Table } from '$lib/design-system';

  export let data: {
    scores: Score[];
    subjects: typeof subjects;
    admin: boolean;
    i: string;
    c: string;
    t: string;
  };

  let mounted = false;

  onMount(() => {
    mounted = true;
  });

  // Calculate average score
  $: averageScore = data.scores.length > 0
    ? Math.round(data.scores.reduce((sum, score) => {
        const scoreValue = score.e || score[1] || score[2] || score[3] || score.p || 0;
        return sum + scoreValue;
      }, 0) / data.scores.length)
    : 0;

  // Get grade based on average
  $: grade = averageScore >= 90 ? 'A+'
    : averageScore >= 80 ? 'A'
    : averageScore >= 70 ? 'B'
    : averageScore >= 60 ? 'C'
    : averageScore >= 50 ? 'D'
    : 'F';
</script>

<div class="apple-page">
  <div class="apple-container">
    <div class="apple-section">
      <!-- Header Section -->
      <div class="mb-16">
        <div class="flex items-center justify-between mb-12">
          <div class="space-y-3">
            <h1 class="apple-heading-1">
              Academic Performance
            </h1>
            <p class="apple-caption uppercase tracking-wide">Term {data.t} • Class {data.c}</p>
          </div>

          {#if data.admin}
            <Button
              href="/s/{data.i}/{data.c}/{data.t}/e"
              variant="primary"
              size="lg"
            >
              Edit Scores
            </Button>
          {/if}
        </div>

        <!-- Stats Cards -->
        {#if data.scores.length > 0}
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <Card variant="elevated" padding="lg">
              <div class="text-center">
                <div class="text-4xl font-light text-gray-900 mb-2">{data.scores.length}</div>
                <div class="apple-caption uppercase tracking-wider">Subjects</div>
              </div>
            </Card>

            <Card variant="elevated" padding="lg">
              <div class="text-center">
                <div class="text-4xl font-light text-gray-900 mb-2">{averageScore}%</div>
                <div class="apple-caption uppercase tracking-wider">Average</div>
              </div>
            </Card>

            <Card variant="elevated" padding="lg">
              <div class="text-center">
                <div class="text-4xl font-light text-gray-900 mb-2">{grade}</div>
                <div class="apple-caption uppercase tracking-wider">Grade</div>
              </div>
            </Card>
          </div>
        {/if}
      </div>

      <!-- Scores Table -->
      {#if data.scores.length > 0}
        <Card variant="elevated" padding="none">
          <Table size="lg" responsive>
            <thead>
              <tr>
                <th scope="col" class="text-left">Subject</th>
                <th scope="col" class="text-right">Score</th>
              </tr>
            </thead>
            <tbody>
              {#each data.scores as score, i}
                <tr
                  class:apple-animate-fade-in={mounted}
                  style="animation-delay: {i * 100}ms"
                >
                  <td>
                    <div class="flex items-center space-x-4">
                      <div class="w-2 h-2 bg-blue-600"></div>
                      <span class="font-medium text-gray-900">
                        {subjects[score.j as keyof typeof subjects]}
                      </span>
                    </div>
                  </td>
                  <td class="text-right">
                    <div class="inline-flex items-center space-x-2">
                      <span class="text-2xl font-light text-gray-900 font-mono">
                        {score.e || score[1] || score[2] || score[3] || score.p || '-'}
                      </span>
                      {#if (score.e || score[1] || score[2] || score[3] || score.p)}
                        <span class="text-sm text-gray-500 font-medium">%</span>
                      {/if}
                    </div>
                  </td>
                </tr>
              {/each}
            </tbody>
          </Table>
        </Card>
      {:else}
        <!-- Empty State -->
        <div class="text-center py-20">
          <div class="w-24 h-24 mx-auto mb-8 bg-gray-100 flex items-center justify-content-center">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 class="apple-heading-4 mb-3">No scores yet</h3>
          <p class="apple-body mb-8 max-w-md mx-auto">
            Academic scores for this term haven't been recorded yet.
          </p>
          {#if data.admin}
            <Button
              href="/s/{data.i}/{data.c}/{data.t}/e"
              variant="primary"
              size="lg"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Scores
            </Button>
          {/if}
        </div>
      {/if}
    </div>
  </div>
</div>