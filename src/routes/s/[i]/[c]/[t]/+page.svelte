<script lang="ts">
  import { subjects } from '$lib/constants';
  import type { Score } from '$lib/types';
  import { onMount } from 'svelte';

  export let data: {
    scores: Score[];
    subjects: typeof subjects;
    admin: boolean;
    i: string;
    c: string;
    t: string;
  };

  let mounted = false;

  onMount(() => {
    mounted = true;
  });

  // Calculate average score
  $: averageScore = data.scores.length > 0
    ? Math.round(data.scores.reduce((sum, score) => {
        const scoreValue = score.e || score[1] || score[2] || score[3] || score.p || 0;
        return sum + scoreValue;
      }, 0) / data.scores.length)
    : 0;

  // Get grade based on average
  $: grade = averageScore >= 90 ? 'A+'
    : averageScore >= 80 ? 'A'
    : averageScore >= 70 ? 'B'
    : averageScore >= 60 ? 'C'
    : averageScore >= 50 ? 'D'
    : 'F';
</script>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
  <div class="max-w-4xl mx-auto px-6 py-12">
    <!-- Header Section -->
    <div class="mb-12">
      <div class="flex items-center justify-between mb-8">
        <div class="space-y-2">
          <h1 class="text-4xl font-light tracking-tight text-slate-900">
            Academic Performance
          </h1>
          <p class="text-lg text-slate-500 font-light">Term {data.t} • Class {data.c}</p>
        </div>

        {#if data.admin}
          <a
            href="/s/{data.i}/{data.c}/{data.t}/e"
            class="group relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl shadow-lg"
          >
            <span class="relative z-10 font-medium">Edit Scores</span>
            <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </a>
        {/if}
      </div>

      <!-- Stats Cards -->
      {#if data.scores.length > 0}
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white/70 backdrop-blur-sm rounded-3xl p-6 shadow-lg border border-white/20">
            <div class="text-3xl font-light text-slate-900 mb-1">{data.scores.length}</div>
            <div class="text-sm font-medium text-slate-500 uppercase tracking-wide">Subjects</div>
          </div>

          <div class="bg-white/70 backdrop-blur-sm rounded-3xl p-6 shadow-lg border border-white/20">
            <div class="text-3xl font-light text-slate-900 mb-1">{averageScore}%</div>
            <div class="text-sm font-medium text-slate-500 uppercase tracking-wide">Average</div>
          </div>

          <div class="bg-white/70 backdrop-blur-sm rounded-3xl p-6 shadow-lg border border-white/20">
            <div class="text-3xl font-light text-slate-900 mb-1">{grade}</div>
            <div class="text-sm font-medium text-slate-500 uppercase tracking-wide">Grade</div>
          </div>
        </div>
      {/if}
    </div>

    <!-- Scores Table -->
    {#if data.scores.length > 0}
      <div class="bg-white/70 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b border-slate-200/50">
                <th class="text-left py-6 px-8 text-sm font-semibold text-slate-600 uppercase tracking-wide">
                  Subject
                </th>
                <th class="text-right py-6 px-8 text-sm font-semibold text-slate-600 uppercase tracking-wide">
                  Score
                </th>
              </tr>
            </thead>
            <tbody>
              {#each data.scores as score, i}
                <tr
                  class="border-b border-slate-100/50 hover:bg-slate-50/50 transition-colors duration-200"
                  class:animate-fade-in={mounted}
                  style="animation-delay: {i * 50}ms"
                >
                  <td class="py-5 px-8">
                    <div class="flex items-center space-x-4">
                      <div class="w-3 h-3 rounded-full bg-gradient-to-r from-blue-400 to-blue-600"></div>
                      <span class="text-slate-900 font-medium">
                        {subjects[score.j as keyof typeof subjects]}
                      </span>
                    </div>
                  </td>
                  <td class="py-5 px-8 text-right">
                    <div class="inline-flex items-center space-x-2">
                      <span class="text-2xl font-light text-slate-900 tabular-nums">
                        {score.e || score[1] || score[2] || score[3] || score.p || '-'}
                      </span>
                      {#if (score.e || score[1] || score[2] || score[3] || score.p)}
                        <span class="text-sm text-slate-500 font-medium">%</span>
                      {/if}
                    </div>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      </div>
    {:else}
      <!-- Empty State -->
      <div class="text-center py-20">
        <div class="w-24 h-24 mx-auto mb-8 rounded-full bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center">
          <svg class="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="text-xl font-light text-slate-900 mb-2">No scores yet</h3>
        <p class="text-slate-500 mb-8 max-w-md mx-auto">
          Academic scores for this term haven't been recorded yet.
        </p>
        {#if data.admin}
          <a
            href="/s/{data.i}/{data.c}/{data.t}/e"
            class="inline-flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span class="font-medium">Add Scores</span>
          </a>
        {/if}
      </div>
    {/if}
  </div>
</div>

<style>
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.6s ease-out forwards;
    opacity: 0;
  }
</style>