<script lang="ts">
  import { subjects } from '$lib/constants';
  import type { Score } from '$lib/types';
  export let data: {
    id: string;
    year: string;
    term: string;
    scores: Score[]
  };

  // Get subject codes already in scores
  $: scored_subjects = data.scores.map(s => s.j);
</script>

<h1>Midterm Scores</h1>
<table>
  <thead>
    <tr>
      <th>Subject</th>
      <th>Score</th>
    </tr>
  </thead>
  <tbody>
    {#each data.scores as score}
      <tr>
        <td>{subjects[score.j as keyof typeof subjects]}</td>
        <td>{score.s}</td>
      </tr>
    {/each}
  </tbody>
</table>