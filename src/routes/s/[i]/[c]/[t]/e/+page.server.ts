import { error, fail, redirect } from '@sveltejs/kit';
import { getById, searchByPayload, upsertPoint } from '$lib/db';
import type { Actions, PageServerLoad } from './$types';
import type { School, SchoolUser, Score } from '$lib/types';
import { requireAuth } from '$lib/auth';

export const load: PageServerLoad = async ({ params, locals }) => {
  const session = await requireAuth(locals);
  // params: id, year, t
  const { i, c, t } = params;
  // Provide all subject codes and names for the form
  const school_user: SchoolUser | null = await getById(i)
  if (!school_user) throw error(401, 'user not found')
  const school: School | null = await getById(school_user.sc)
  if (!school) throw error(401, 'school not found')

  const scores: Score[] = await searchByPayload({
    s: 'sch_scr',
    u: i,
    c: c,
    y: 2024,
    t: t
  });

  return {
    i,
    c: school.c[c],
    t,
    scores,
  };
};

export const actions: Actions = {
  default: async ({ request, params }) => {
    const form = await request.formData();
    const { i, c, t } = params;
    // id: uuid (string), c: number (class key), t: number (t number)
    if (!i || !c || !t) {
      return fail(400, { error: 'Missing student, class, or t' });
    }
    const classKey = parseInt(c);
    const termNum = parseInt(t);
    try {
      // For each subject, get the score from the form and create a Score
      // For each field in the form that starts with "score_", add a score to the database
      for (const [key, value] of form.entries()) {
        if (key.startsWith('score_')) {
          const subjectCode = key.slice('score_'.length);
          if (value !== null && value !== '') {
            const score: Score = {
              s: 'sch_scr',
              u: i,
              t: termNum as 1 | 2 | 3,
              c: classKey,
              y: 2024,
              j: subjectCode,
              e: parseInt(value.toString())
            };
            await upsertPoint(score);
          }
        }
      }
      // Redirect back to student page or success page
      redirect(303, `/s/${i}/${c}/${t}`);
    } catch (error) {
      console.error('Error saving scores:', error);
      return fail(500, { error: 'Failed to save scores' });
    }
  }
};
