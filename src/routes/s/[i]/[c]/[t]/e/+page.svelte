<script lang="ts">
  import { page } from '$app/stores';
	import { subjects } from '$lib/constants';
	import type { Score } from '$lib/types';
  import { onMount } from 'svelte';
  import { <PERSON><PERSON>, <PERSON>alogHeader, DialogTitle, DialogContent } from '$lib/components/ui/dialog';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
	import { enhance } from '$app/forms';
  export let data: {
    id: string;
    year: string;
    term: string;
    scores: Score[]
  };

  // keys of added subjects
  let added_subjects: string[] = [];
  let showModal = false;

  // Get subject codes already in scores
  $: scored_subjects = data.scores.map(s => s.j);
  // Subjects not yet scored
  $: available_subjects = Object.keys(subjects).filter(code => !scored_subjects.includes(code));
  // Add all toggle
  let addAll = false;
  $: if (addAll) {
    added_subjects = [...available_subjects];
  } else if (added_subjects.length === available_subjects.length) {
    addAll = true;
  }
  function toggleSubject(code: string) {
    if (added_subjects.includes(code)) {
      added_subjects = added_subjects.filter(s => s !== code);
      addAll = false;
    } else {
      added_subjects = [...added_subjects, code];
      if (added_subjects.length === available_subjects.length) addAll = true;
    }
  }
  function toggleAddAll() {
    addAll = !addAll;
    if (addAll) {
      added_subjects = [...available_subjects];
    } else {
      added_subjects = [];
    }
  }
</script>

<h1>Edit Midterm Scores</h1>
<Button on:click={() => showModal = true} type="button" variant="outline" extraClass="mb-4">Add Subject</Button>
<form use:enhance method="POST">
  <input type="hidden" name="studentId" value={data.id} />
  <!-- <input type="hidden" name="year" value={data.year} /> -->
  <input type="hidden" name="term" value={data.term} />
  <table>
    <thead>
      <tr>
        <th>Subject</th>
        <th>Score</th>
      </tr>
    </thead>
    <tbody>
      {#each data.scores as score}
        <tr>
          <td>{subjects[score.j]}</td>
          <td>
            <input
              type="number"
              name={`score_${score.j}`}
              min="0"
              max="100"
              step="1"
              value=""
              placeholder="Enter score"
              required
            />
          </td>
        </tr>
      {/each}
      {#each added_subjects as subject}
        <tr>
          <td>{subjects[subject as keyof typeof subjects]}</td>
          <td>
            <input type="number" name={`score_${subject}`} min="0" max="100" step="1" value="" placeholder="Enter score" required />
          </td>
        </tr>
      {/each}
    </tbody>
  </table>

  <button type="submit">Save Scores</button>
</form>

<Dialog bind:open={showModal}>
  <DialogHeader>
    <DialogTitle>Add Subjects</DialogTitle>
  </DialogHeader>
  <DialogContent>
    {#if available_subjects.length === 0}
      <p>All subjects already have scores.</p>
    {:else}
      <div style="margin-bottom: 1rem;">
        <Label>
          <input type="checkbox" checked={addAll} on:change={toggleAddAll} /> Add all
        </Label>
      </div>
      <div style="max-height: 300px; overflow-y: auto;">
        {#each available_subjects as code}
          <div style="margin-bottom: 0.5rem; display: flex; align-items: center;">
            <Label>
              <input
                type="checkbox"
                checked={added_subjects.includes(code)}
                on:change={() => toggleSubject(code)}
              />
              {subjects[code as keyof typeof subjects]}
            </Label>
          </div>
        {/each}
      </div>
      <div style="margin-top: 1.5rem; display: flex; gap: 1rem;">
        <Button type="button" variant="primary" on:click={() => showModal = false}>Done</Button>
        <Button type="button" variant="outline" on:click={() => { showModal = false; added_subjects = []; addAll = false; }}>Cancel</Button>
      </div>
    {/if}
  </DialogContent>
</Dialog> 