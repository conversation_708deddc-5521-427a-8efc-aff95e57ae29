<script lang="ts">
	import { subjects } from '$lib/constants';
	import type { Score } from '$lib/types';
  import { onMount } from 'svelte';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
  import { Button, Card, Input } from '$lib/design-system';

  export let data: {
    i: string;
    c: string;
    t: string;
    scores: Score[]
  };

  let mounted = false;
  let showAddSubjects = false;
  let added_subjects: string[] = [];
  let isSubmitting = false;

  // Get subject codes already in scores
  $: scored_subjects = data.scores.map(s => s.j);
  // Subjects not yet scored
  $: available_subjects = Object.keys(subjects).filter(code => !scored_subjects.includes(code));

  onMount(() => {
    mounted = true;
  });

  function toggleSubject(code: string) {
    if (added_subjects.includes(code)) {
      added_subjects = added_subjects.filter(s => s !== code);
    } else {
      added_subjects = [...added_subjects, code];
    }
  }
</script>

<div class="apple-page">
  <div class="apple-container">
    <div class="apple-section">
      <!-- Header Section -->
      <div class="mb-16">
        <div class="flex items-center justify-between mb-12">
          <div class="space-y-3">
            <h1 class="apple-heading-1">
              Edit Scores
            </h1>
            <p class="apple-caption uppercase tracking-wide">Term {data.t} • Class {data.c}</p>
          </div>

          <Button
            href="/s/{data.i}/{data.c}/{data.t}"
            variant="secondary"
            size="lg"
          >
            ← Back to View
          </Button>
        </div>

        <!-- Add Subjects Button -->
        {#if available_subjects.length > 0}
          <Button
            on:click={() => showAddSubjects = !showAddSubjects}
            variant="primary"
            size="md"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Subjects
          </Button>
        {/if}

        <!-- Add Subjects Panel -->
        {#if showAddSubjects && available_subjects.length > 0}
          <Card variant="elevated" padding="lg" class="mb-8">
            <h3 class="apple-heading-4 mb-6">Select subjects to add</h3>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
              {#each available_subjects as code}
                <label class="flex items-center space-x-3 p-4 border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition cursor-pointer">
                  <input
                    type="checkbox"
                    checked={added_subjects.includes(code)}
                    on:change={() => toggleSubject(code)}
                    class="w-5 h-5 text-blue-600 border-2 border-gray-300 focus:ring-blue-500 focus:ring-2"
                  />
                  <span class="text-gray-700 font-medium">{subjects[code as keyof typeof subjects]}</span>
                </label>
              {/each}
            </div>
            <div class="flex space-x-4">
              <Button
                on:click={() => showAddSubjects = false}
                variant="primary"
              >
                Done
              </Button>
              <Button
                on:click={() => { showAddSubjects = false; added_subjects = []; }}
                variant="secondary"
              >
                Cancel
              </Button>
            </div>
          </Card>
        {/if}
      </div>

      <!-- Scores Form -->
      <form
        use:enhance={() => {
          isSubmitting = true;
          return ({ result }) => {
            isSubmitting = false;
            if (result.type === 'success') {
              goto(`/s/${data.i}/${data.c}/${data.t}`);
            }
          }
        }}
        method="POST"
        class="space-y-8"
      >
        <input type="hidden" name="studentId" value={data.i} />
        <input type="hidden" name="term" value={data.t} />

        {#if data.scores.length > 0 || added_subjects.length > 0}
          <Card variant="elevated" padding="lg">
            <h2 class="apple-heading-3 mb-8">Subject Scores</h2>

            <div class="space-y-6">
              <!-- Existing Scores -->
              {#each data.scores as score, i}
                <div
                  class="group p-6 border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition"
                  class:apple-animate-fade-in={mounted}
                  style="animation-delay: {i * 100}ms"
                >
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                      <div class="w-3 h-3 bg-blue-600"></div>
                      <label for="score_{score.j}" class="text-lg font-medium text-gray-900">
                        {subjects[score.j as keyof typeof subjects]}
                      </label>
                    </div>
                    <div class="flex items-center space-x-4">
                      <Input
                        id="score_{score.j}"
                        type="number"
                        name={`score_${score.j}`}
                        min="0"
                        max="100"
                        step="1"
                        value={score.e || score[1] || score[2] || score[3] || score.p || ''}
                        placeholder="0"
                        size="sm"
                        fullWidth={false}
                        disabled={isSubmitting}
                      />
                      <span class="text-gray-500 font-medium">%</span>
                    </div>
                  </div>
                </div>
              {/each}

              <!-- Added Subjects -->
              {#each added_subjects as subject, i}
                <div
                  class="group p-6 border border-green-200 bg-green-50 hover:border-green-300 hover:bg-green-100 transition"
                  class:apple-animate-fade-in={mounted}
                  style="animation-delay: {(data.scores.length + i) * 100}ms"
                >
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                      <div class="w-3 h-3 bg-green-600"></div>
                      <label for="score_{subject}" class="text-lg font-medium text-gray-900">
                        {subjects[subject as keyof typeof subjects]}
                      </label>
                      <span class="text-xs bg-green-100 text-green-700 px-2 py-1 font-medium uppercase tracking-wide">NEW</span>
                    </div>
                    <div class="flex items-center space-x-4">
                      <Input
                        id="score_{subject}"
                        type="number"
                        name={`score_${subject}`}
                        min="0"
                        max="100"
                        step="1"
                        value=""
                        placeholder="0"
                        size="sm"
                        fullWidth={false}
                        disabled={isSubmitting}
                        required
                      />
                      <span class="text-gray-500 font-medium">%</span>
                    </div>
                  </div>
                </div>
              {/each}
            </div>
          </Card>

          <!-- Submit Button -->
          <div class="flex justify-center pt-8">
            <Button
              type="submit"
              variant="primary"
              size="lg"
              disabled={isSubmitting}
              loading={isSubmitting}
            >
              {#if isSubmitting}
                <svg class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Saving...
              {:else}
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                Save Scores
              {/if}
            </Button>
          </div>
        {:else}
          <!-- Empty State -->
          <div class="text-center py-20">
            <div class="w-24 h-24 mx-auto mb-8 bg-gray-100 flex items-center justify-center">
              <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <h3 class="apple-heading-4 mb-3">No subjects to score</h3>
            <p class="apple-body mb-8 max-w-md mx-auto">
              Add subjects to start recording scores for this term.
            </p>
            <Button
              on:click={() => showAddSubjects = true}
              variant="primary"
              size="lg"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Subjects
            </Button>
          </div>
        {/if}
      </form>
    </div>
  </div>
</div>