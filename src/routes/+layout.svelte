<script lang="ts">
	import '../app.css';
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { signIn, signOut } from '@auth/sveltekit/client';
	import { fly, fade } from 'svelte/transition';

	let mounted = false;

	onMount(async () => {
		mounted = true;
		
		// Commented out anime.js animations
		/*
		const anime = ((await import('animejs')) as any).default || (await import('animejs'));
		
		const tl = anime.timeline({
			easing: 'easeInOutSine',
			loop: true,
			direction: 'alternate'
		});

		tl.add({
			targets: '.float-element',
			translateY: [-20, 20],
			duration: 4000,
			delay: anime.stagger(300)
		});
		*/
	});
</script>

<div class="min-h-screen gradient-bg relative overflow-hidden">
	<!-- Floating background elements -->
	<div class="absolute inset-0 overflow-hidden pointer-events-none">
		<div class="float-element absolute top-20 left-10 w-96 h-96 bg-blue-200/20 rounded-full blur-3xl"></div>
		<div class="float-element absolute bottom-20 right-20 w-80 h-80 bg-sky-300/20 rounded-full blur-3xl"></div>
		<div class="float-element absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-amber-200/20 rounded-full blur-3xl"></div>
	</div>
	
	<!-- Navigation -->
	<nav class="glassmorphic sticky top-0 z-50 border-b border-white/10">
		<div class="container mx-auto px-6 py-4">
			<div class="flex items-center justify-between">
				<a href="/" class="text-2xl font-bold text-primary flex items-center gap-2">
					<div class="w-10 h-10 rounded-xl bg-gradient-to-br from-primary to-accent flex items-center justify-center text-white">
						S
					</div>
					School Portal
				</a>
				
				<div class="flex items-center gap-4">
					{#if $page.data.session}
						<div class="flex items-center gap-2">
							{#if $page.data.session.user?.image}
								<img src={$page.data.session.user.image} alt="User" class="w-8 h-8 rounded-full" />
							{/if}
							<span class="text-sm font-medium">
								{$page.data.session.user?.name ?? 'User'}
							</span>
							<button 
								on:click={() => signOut()}
								class="btn btn-sm btn-outline"
							>
								Sign out
							</button>
						</div>
					{:else}
						<button 
							on:click={() => signIn('google')}
							class="btn btn-sm btn-primary"
						>
							Sign in
						</button>
					{/if}
				</div>
			</div>
		</div>
	</nav>
	
	<!-- Main content -->
	<main class="relative z-10 container mx-auto px-6 py-8">
		{#if mounted}
			<div in:fade={{ duration: 300 }}>
				<slot />
			</div>
		{/if}
	</main>
</div>

<style>
	:global(body) {
		overflow-x: hidden;
	}
</style>
