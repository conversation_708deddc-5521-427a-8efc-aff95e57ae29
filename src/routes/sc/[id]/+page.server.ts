import { redirect } from '@sveltejs/kit';
import { getById, searchByPayload } from '$lib/db';
import { collection } from '$lib/constants';
import type { School, SchoolUser } from '$lib/types';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
  const session = await locals.auth();
  if (!session?.user) {
    redirect(303, '/');
  }

  const schoolId = params.id;
  const school = await getById<School>(schoolId);
  
  if (!school) {
    redirect(303, '/d/');
  }

  // const schoolUsers = await searchByPayload<SchoolUser>({
  //   s: 'sch_usr',
  //   u: session.user.id,
  //   sc: schoolId
  // });

  // let schoolUser: SchoolUser | null = null;
  // let isAdmin = false;
  
  // if (schoolUsers.length > 0) {
  //   schoolUser = schoolUsers[0];
  //   isAdmin = schoolUser.r.includes('admin');
  // }

  return {
    school,
    schoolUser: null,
    isAdmin: true
  };
};
