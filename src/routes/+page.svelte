<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { signIn } from '@auth/sveltekit/client';
  
  let isLoaded = false;
  $: isLoggedIn = !!$page.data.session;
  
  onMount(async () => {
    isLoaded = true;
  });
</script>

<div class="min-h-screen flex items-center justify-center relative">
  <!-- Subtle background elements -->
  <div class="absolute inset-0 opacity-40">
    <div class="absolute top-20 left-10 w-64 h-64 bg-blue-100 blur-3xl float-element"></div>
    <div class="absolute bottom-20 right-10 w-96 h-96 bg-gray-100 blur-3xl float-element" style="animation-delay: 1s"></div>
    <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-blue-50 blur-3xl"></div>
  </div>

  <div class="relative z-10 w-full max-w-5xl mx-auto px-6">
    <div class="text-center mb-16">
      <h1 class="apple-heading-1 text-6xl md:text-7xl font-light text-gray-900 mb-6">
        School Portal
      </h1>
      <p class="apple-body text-xl md:text-2xl text-gray-600">
        Manage academic records with elegance and ease
      </p>
    </div>
    
    {#if isLoggedIn}
      <div class="card-glass max-w-md mx-auto">
        <div class="text-center space-y-8">
          <div class="w-24 h-24 mx-auto bg-blue-600 flex items-center justify-center text-white text-4xl shadow-lg">
            🏫
          </div>

          <h2 class="apple-heading-4 text-gray-800">Welcome, {$page.data.session?.user?.name}</h2>

          <p class="apple-body text-gray-600">
            Create a new school or access your existing ones
          </p>

          <a
            href="/sc/new"
            class="btn btn-primary w-full flex items-center justify-center gap-3"
          >
            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
            </svg>
            <span>Add New School</span>
          </a>

          <a
            href="/d"
            class="text-sm text-gray-500 hover:text-primary transition-colors"
          >
            Or go to dashboard →
          </a>
        </div>
      </div>
    {:else}
      <div class="card-glass max-w-md mx-auto">
        <div class="text-center space-y-8">
          <div class="w-24 h-24 mx-auto bg-blue-600 flex items-center justify-center text-white text-4xl shadow-lg">
            🎓
          </div>

          <h2 class="apple-heading-4 text-gray-800">Welcome Back</h2>

          <p class="apple-body text-gray-600">
            Sign in to access your school dashboard
          </p>

          {#if $page.url.searchParams.get('error')}
            <div class="text-red-600 text-sm bg-red-50 border border-red-200 p-4">
              {$page.url.searchParams.get('error') === 'invalid_auth' ? 'Invalid authentication data' : 'Authentication failed. Please try again.'}
            </div>
          {/if}

          <button
            on:click={() => signIn('google')}
            class="btn btn-primary w-full flex items-center justify-center gap-3"
          >
            <svg class="w-5 h-5" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            <span>Continue with Google</span>
          </button>

          <p class="apple-caption text-gray-500">
            By signing in, you agree to our terms of service and privacy policy
          </p>
        </div>
      </div>
    {/if}
    
    {#if isLoaded}
      <!-- Decorative floating elements -->
      <div class="absolute top-10 right-20 text-6xl opacity-20 float-element" style="animation-delay: 0.5s">📚</div>
      <div class="absolute bottom-10 left-20 text-6xl opacity-20 float-element" style="animation-delay: 1.5s">✏️</div>
      <div class="absolute top-1/3 left-10 text-5xl opacity-20 float-element" style="animation-delay: 2s">📝</div>
    {/if}
  </div>
</div>
