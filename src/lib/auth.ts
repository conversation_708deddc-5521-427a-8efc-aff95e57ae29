import { searchByPayload, upsertPoint } from '$lib/db';
import { redirect } from '@sveltejs/kit';
import type { User } from './types';

export async function find_or_create_user(googleUser: {
  id: string;
  email: string;
  name: string;
}): Promise<User> {
  // Check if user exists
  const existingUsers = await searchByPayload<User>({ 
    g: googleUser.id,
    s: 'u'
  }, 1);
  
  if (existingUsers.length > 0) {
    return existingUsers[0];
  }
  
  // Create new user
  const newUser: User = {
    s: 'u',
    n: googleUser.name,
    e: googleUser.email,
    g: googleUser.id
  };
  
  return await upsertPoint(newUser);
}

export async function requireAuth(locals: App.Locals) {
    const session = await locals.auth();
    if (!session?.user) throw redirect(303, '/auth');
    return session;
}