import { QDRANT_KEY, QDRANT_URL } from '$env/static/private';
import { QdrantClient } from '@qdrant/js-client-rest';
import { v7 as uuidv7 } from 'uuid';
import { collection } from './constants';
import type { User } from './types';

// Qdrant client configuration
export const qdrant = new QdrantClient({
	url: QDRANT_URL || 'http://localhost:6333',
	apiKey: QDRANT_KEY
});


// Utility functions
export function generateId(): string {
	return uuidv7();
}

// Initialize collections
export async function initializeCollections() {
	const collections = Object.values(COLLECTIONS);

	for (const collection of collections) {
		try {
			await qdrant.getCollection(collection);
		} catch (error) {
			// Collection doesn't exist, create it
			await qdrant.createCollection(collection, {
				vectors: {
					size: 4,
					distance: 'Dot'
				}
			});
		}
	}
}

// Database operations wrapper
export async function upsertPoint<T extends { id?: string; s: string }>(
	data: T
) {
	const id = data.id || generateId();

	const vector = new Array(768).fill(0);

	await qdrant.upsert(collection, {
		points: [
			{
				id,
				payload: { ...data, id },
				vector
			}
		]
	});

	return { ...data, id };
}

export async function searchByPayload<T>(
	filters: Record<string, any>,
	limit: number = 27
): Promise<T[]> {
	const mustFilters = Object.entries(filters).map(([key, value]) => ({
		key,
		match: { value }
	}));

	const results = await qdrant.scroll(collection, {
		filter: {
			must: mustFilters
		},
		limit,
		with_payload: true,
		with_vector: false
	});

	// console.debug('searchByPayload results', results);

	return results.points.map((point) => point.payload as T);
}

export async function searchByName<T>(
	collection: string,
	name: string,
	tenantId: string,
	limit: number = 3
): Promise<T[]> {
	// For now, we'll do a simple scroll with name filtering
	// In production, you'd want to use proper text search
	const results = await qdrant.scroll(collection, {
		filter: {
			must: [{ key: 's', match: { value: tenantId } }]
		},
		limit: 100,
		with_payload: true,
		with_vector: false
	});

	// Client-side name filtering (in production, use proper text search)
	const filtered = results.points
		.filter((point) =>
			(
				(point.payload as Record<string, any>).n?.toLowerCase() || ''
			).includes(name.toLowerCase())
		)
		.slice(0, limit);

	return filtered.map((point) => point.payload as T);
}

export async function getById<T>(
	id: string
): Promise<T | null> {
	try {
		const result = await qdrant.retrieve(collection, {
			ids: [id],
			with_payload: true,
			with_vector: false
		});
		
		if (result.length > 0) {
			return result[0].payload as T;
		}
		return null;
	} catch {
		return null;
	}
}

export async function deleteById(
	collection: string,
	id: string
): Promise<void> {
	await qdrant.delete(collection, {
		points: [id]
	});
}

export async function updatePoint<T extends { id: string; s: string }>(
	collection: string,
	id: string,
	data: Partial<T>
): Promise<void> {
	const existing = await getById<T>(collection, id);
	if (!existing) {
		throw new Error('Document not found');
	}

	await upsertPoint(collection, { ...existing, ...data, id });
}

// Get user's name from their ID
export async function getUserNameFromId(
	userId: string
): Promise<string> {
	const user = await getById<User>(COLLECTIONS.USERS, userId);

	if (user) {
		return user.n;
	}

	// If user not found, return Unknown User
	return 'Unknown User';
}
