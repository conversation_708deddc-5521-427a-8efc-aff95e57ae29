<script lang="ts">
  export let variant: 'default' | 'striped' | 'bordered' = 'default';
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let stickyHeader = false;
  export let responsive = true;

  $: tableClass = [
    'apple-table',
    `apple-table--${variant}`,
    `apple-table--${size}`,
    stickyHeader && 'apple-table--sticky-header'
  ].filter(Boolean).join(' ');

  $: wrapperClass = [
    'apple-table-wrapper',
    responsive && 'apple-table-wrapper--responsive'
  ].filter(Boolean).join(' ');
</script>

<div class={wrapperClass}>
  <table class={tableClass} {...$$restProps}>
    <slot />
  </table>
</div>

<style>
  .apple-table-wrapper {
    position: relative;
    background-color: var(--color-white);
    border: 1px solid var(--color-gray-200);
    
    /* Zero border radius for Apple flat design */
    border-radius: var(--radius-none);
  }

  .apple-table-wrapper--responsive {
    overflow-x: auto;
  }

  .apple-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--font-family-sans);
    background-color: transparent;
  }

  /* Size variants */
  .apple-table--sm {
    font-size: var(--text-sm);
  }

  .apple-table--sm :global(th),
  .apple-table--sm :global(td) {
    padding: var(--space-2) var(--space-3);
  }

  .apple-table--md {
    font-size: var(--text-base);
  }

  .apple-table--md :global(th),
  .apple-table--md :global(td) {
    padding: var(--space-3) var(--space-4);
  }

  .apple-table--lg {
    font-size: var(--text-lg);
  }

  .apple-table--lg :global(th),
  .apple-table--lg :global(td) {
    padding: var(--space-4) var(--space-6);
  }

  /* Header styles */
  .apple-table :global(thead) {
    background-color: var(--color-gray-50);
  }

  .apple-table :global(th) {
    font-weight: var(--font-semibold);
    color: var(--color-gray-700);
    text-align: left;
    border-bottom: 1px solid var(--color-gray-200);
    position: relative;
  }

  .apple-table--sticky-header :global(th) {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--color-gray-50);
  }

  /* Body styles */
  .apple-table :global(tbody tr) {
    transition: background-color var(--transition-fast);
  }

  .apple-table :global(tbody tr:hover) {
    background-color: var(--color-gray-50);
  }

  .apple-table :global(td) {
    color: var(--color-gray-900);
    border-bottom: 1px solid var(--color-gray-100);
    vertical-align: top;
  }

  /* Variant styles */
  .apple-table--striped :global(tbody tr:nth-child(even)) {
    background-color: var(--color-gray-50);
  }

  .apple-table--striped :global(tbody tr:nth-child(even):hover) {
    background-color: var(--color-gray-100);
  }

  .apple-table--bordered :global(th),
  .apple-table--bordered :global(td) {
    border: 1px solid var(--color-gray-200);
  }

  .apple-table--bordered {
    border: 1px solid var(--color-gray-200);
  }

  /* Footer styles */
  .apple-table :global(tfoot) {
    background-color: var(--color-gray-50);
    border-top: 2px solid var(--color-gray-200);
  }

  .apple-table :global(tfoot th),
  .apple-table :global(tfoot td) {
    font-weight: var(--font-semibold);
    color: var(--color-gray-700);
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .apple-table--lg {
      font-size: var(--text-base);
    }

    .apple-table--lg :global(th),
    .apple-table--lg :global(td) {
      padding: var(--space-3) var(--space-4);
    }
  }

  /* Accessibility improvements */
  .apple-table :global(th[scope="col"]) {
    text-align: left;
  }

  .apple-table :global(th[scope="row"]) {
    font-weight: var(--font-medium);
  }

  /* Focus styles for interactive elements within table */
  .apple-table :global(a:focus),
  .apple-table :global(button:focus) {
    outline: 2px solid var(--color-blue-500);
    outline-offset: 2px;
  }

  .apple-table :global(a:focus:not(:focus-visible)),
  .apple-table :global(button:focus:not(:focus-visible)) {
    outline: none;
  }
</style>
