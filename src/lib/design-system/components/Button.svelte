<script lang="ts">
  export let variant: 'primary' | 'secondary' | 'ghost' | 'danger' = 'primary';
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let disabled = false;
  export let loading = false;
  export let href: string | undefined = undefined;
  export let type: 'button' | 'submit' | 'reset' = 'button';
  export let fullWidth = false;

  $: component = href ? 'a' : 'button';
  $: buttonClass = [
    'apple-button',
    `apple-button--${variant}`,
    `apple-button--${size}`,
    disabled && 'apple-button--disabled',
    loading && 'apple-button--loading',
    fullWidth && 'apple-button--full-width'
  ].filter(Boolean).join(' ');
</script>

<svelte:element
  this={component}
  class={buttonClass}
  {type}
  {href}
  {disabled}
  on:click
  on:focus
  on:blur
  on:mouseenter
  on:mouseleave
  {...$$restProps}
>
  {#if loading}
    <span class="apple-button__spinner" aria-hidden="true">
      <svg viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
      </svg>
    </span>
  {/if}
  
  <span class="apple-button__content" class:apple-button__content--hidden={loading}>
    <slot />
  </span>
</svelte:element>

<style>
  .apple-button {
    /* Base styles */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family-sans);
    font-weight: var(--font-medium);
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    
    /* Zero border radius for Apple flat design */
    border-radius: var(--radius-none);
    
    /* Focus styles */
    &:focus {
      outline: 2px solid var(--color-blue-500);
      outline-offset: 2px;
    }
    
    &:focus:not(:focus-visible) {
      outline: none;
    }
  }

  /* Size variants */
  .apple-button--sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
    min-height: 32px;
  }

  .apple-button--md {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-base);
    min-height: 40px;
  }

  .apple-button--lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
    min-height: 48px;
  }

  /* Color variants */
  .apple-button--primary {
    background-color: var(--color-blue-600);
    color: var(--color-white);
    box-shadow: var(--shadow-sm);
    
    &:hover:not(:disabled) {
      background-color: var(--color-blue-700);
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }
    
    &:active:not(:disabled) {
      background-color: var(--color-blue-700);
      transform: translateY(0);
      box-shadow: var(--shadow-sm);
    }
  }

  .apple-button--secondary {
    background-color: var(--color-white);
    color: var(--color-gray-700);
    border: 1px solid var(--color-gray-300);
    box-shadow: var(--shadow-sm);
    
    &:hover:not(:disabled) {
      background-color: var(--color-gray-50);
      border-color: var(--color-gray-400);
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }
    
    &:active:not(:disabled) {
      background-color: var(--color-gray-100);
      transform: translateY(0);
      box-shadow: var(--shadow-sm);
    }
  }

  .apple-button--ghost {
    background-color: transparent;
    color: var(--color-gray-700);
    
    &:hover:not(:disabled) {
      background-color: var(--color-gray-100);
    }
    
    &:active:not(:disabled) {
      background-color: var(--color-gray-200);
    }
  }

  .apple-button--danger {
    background-color: var(--color-red-600);
    color: var(--color-white);
    box-shadow: var(--shadow-sm);
    
    &:hover:not(:disabled) {
      background-color: var(--color-red-700);
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);
    }
    
    &:active:not(:disabled) {
      background-color: var(--color-red-700);
      transform: translateY(0);
      box-shadow: var(--shadow-sm);
    }
  }

  /* States */
  .apple-button--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }

  .apple-button--loading {
    cursor: not-allowed;
  }

  .apple-button--full-width {
    width: 100%;
  }

  /* Spinner */
  .apple-button__spinner {
    position: absolute;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
  }

  .apple-button__spinner svg {
    width: 100%;
    height: 100%;
  }

  .apple-button__content {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    transition: opacity var(--transition-fast);
  }

  .apple-button__content--hidden {
    opacity: 0;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .apple-button--lg {
      padding: var(--space-3) var(--space-6);
      font-size: var(--text-base);
      min-height: 44px;
    }
  }
</style>
