<script lang="ts">
  export let type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search' = 'text';
  export let value: string | number = '';
  export let placeholder = '';
  export let disabled = false;
  export let readonly = false;
  export let required = false;
  export let error = false;
  export let errorMessage = '';
  export let label = '';
  export let hint = '';
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let fullWidth = true;
  export let id = '';

  let inputElement: HTMLInputElement;
  let focused = false;

  $: hasValue = value !== '' && value !== null && value !== undefined;
  $: inputClass = [
    'apple-input',
    `apple-input--${size}`,
    error && 'apple-input--error',
    disabled && 'apple-input--disabled',
    readonly && 'apple-input--readonly',
    focused && 'apple-input--focused',
    fullWidth && 'apple-input--full-width'
  ].filter(Boolean).join(' ');

  function handleFocus() {
    focused = true;
  }

  function handleBlur() {
    focused = false;
  }
</script>

<div class="apple-input-wrapper" class:apple-input-wrapper--full-width={fullWidth}>
  {#if label}
    <label for={id} class="apple-input-label">
      {label}
      {#if required}
        <span class="apple-input-required" aria-label="required">*</span>
      {/if}
    </label>
  {/if}

  <div class="apple-input-container">
    <input
      bind:this={inputElement}
      bind:value
      {type}
      {placeholder}
      {disabled}
      {readonly}
      {required}
      {id}
      class={inputClass}
      on:focus={handleFocus}
      on:blur={handleBlur}
      on:input
      on:change
      on:keydown
      on:keyup
      on:keypress
      {...$$restProps}
    />
  </div>

  {#if hint && !error}
    <p class="apple-input-hint">{hint}</p>
  {/if}

  {#if error && errorMessage}
    <p class="apple-input-error">{errorMessage}</p>
  {/if}
</div>

<style>
  .apple-input-wrapper {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
  }

  .apple-input-wrapper--full-width {
    width: 100%;
  }

  .apple-input-label {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--color-gray-700);
    line-height: var(--leading-snug);
  }

  .apple-input-required {
    color: var(--color-red-500);
    margin-left: var(--space-1);
  }

  .apple-input-container {
    position: relative;
  }

  .apple-input {
    /* Base styles */
    width: 100%;
    font-family: var(--font-family-sans);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    color: var(--color-gray-900);
    background-color: var(--color-white);
    border: 1px solid var(--color-gray-300);
    transition: all var(--transition-fast);
    
    /* Zero border radius for Apple flat design */
    border-radius: var(--radius-none);
    
    /* Focus styles */
    &:focus {
      outline: none;
      border-color: var(--color-blue-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    /* Placeholder styles */
    &::placeholder {
      color: var(--color-gray-400);
    }
    
    /* Autofill styles */
    &:-webkit-autofill {
      -webkit-box-shadow: 0 0 0 1000px var(--color-white) inset;
      -webkit-text-fill-color: var(--color-gray-900);
    }
  }

  /* Size variants */
  .apple-input--sm {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-sm);
    min-height: 32px;
  }

  .apple-input--md {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-base);
    min-height: 40px;
  }

  .apple-input--lg {
    padding: var(--space-4) var(--space-5);
    font-size: var(--text-lg);
    min-height: 48px;
  }

  /* State variants */
  .apple-input--error {
    border-color: var(--color-red-500);
    
    &:focus {
      border-color: var(--color-red-500);
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
  }

  .apple-input--disabled {
    background-color: var(--color-gray-100);
    color: var(--color-gray-500);
    cursor: not-allowed;
    
    &::placeholder {
      color: var(--color-gray-400);
    }
  }

  .apple-input--readonly {
    background-color: var(--color-gray-50);
    cursor: default;
  }

  .apple-input--focused {
    border-color: var(--color-blue-500);
  }

  .apple-input--full-width {
    width: 100%;
  }

  /* Helper text */
  .apple-input-hint {
    font-size: var(--text-xs);
    color: var(--color-gray-500);
    line-height: var(--leading-snug);
    margin: 0;
  }

  .apple-input-error {
    font-size: var(--text-xs);
    color: var(--color-red-600);
    line-height: var(--leading-snug);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-1);
  }

  .apple-input-error::before {
    content: '⚠';
    font-size: var(--text-sm);
  }

  /* Number input specific styles */
  .apple-input[type="number"] {
    -moz-appearance: textfield;
  }

  .apple-input[type="number"]::-webkit-outer-spin-button,
  .apple-input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Search input specific styles */
  .apple-input[type="search"] {
    -webkit-appearance: none;
  }

  .apple-input[type="search"]::-webkit-search-decoration,
  .apple-input[type="search"]::-webkit-search-cancel-button,
  .apple-input[type="search"]::-webkit-search-results-button,
  .apple-input[type="search"]::-webkit-search-results-decoration {
    -webkit-appearance: none;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .apple-input--lg {
      padding: var(--space-3) var(--space-4);
      font-size: var(--text-base);
      min-height: 44px;
    }
  }
</style>
