<script lang="ts">
  export let variant: 'default' | 'elevated' | 'outlined' | 'ghost' = 'default';
  export let padding: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  export let interactive = false;
  export let href: string | undefined = undefined;

  $: component = href ? 'a' : 'div';
  $: cardClass = [
    'apple-card',
    `apple-card--${variant}`,
    `apple-card--padding-${padding}`,
    interactive && 'apple-card--interactive',
    href && 'apple-card--link'
  ].filter(Boolean).join(' ');
</script>

<svelte:element
  this={component}
  class={cardClass}
  {href}
  on:click
  on:focus
  on:blur
  on:mouseenter
  on:mouseleave
  {...$$restProps}
>
  <slot />
</svelte:element>

<style>
  .apple-card {
    /* Base styles */
    display: block;
    position: relative;
    transition: all var(--transition-normal);
    
    /* Zero border radius for Apple flat design */
    border-radius: var(--radius-none);
    
    /* Focus styles for interactive cards */
    &:focus {
      outline: 2px solid var(--color-blue-500);
      outline-offset: 2px;
    }
    
    &:focus:not(:focus-visible) {
      outline: none;
    }
  }

  /* Variant styles */
  .apple-card--default {
    background-color: var(--color-white);
    border: 1px solid var(--color-gray-200);
  }

  .apple-card--elevated {
    background-color: var(--color-white);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--color-gray-100);
  }

  .apple-card--outlined {
    background-color: transparent;
    border: 1px solid var(--color-gray-300);
  }

  .apple-card--ghost {
    background-color: transparent;
    border: none;
  }

  /* Padding variants */
  .apple-card--padding-none {
    padding: 0;
  }

  .apple-card--padding-sm {
    padding: var(--space-4);
  }

  .apple-card--padding-md {
    padding: var(--space-6);
  }

  .apple-card--padding-lg {
    padding: var(--space-8);
  }

  .apple-card--padding-xl {
    padding: var(--space-12);
  }

  /* Interactive states */
  .apple-card--interactive {
    cursor: pointer;
  }

  .apple-card--interactive:hover {
    transform: translateY(-1px);
  }

  .apple-card--interactive.apple-card--default:hover {
    border-color: var(--color-gray-300);
    box-shadow: var(--shadow-md);
  }

  .apple-card--interactive.apple-card--elevated:hover {
    box-shadow: var(--shadow-xl);
  }

  .apple-card--interactive.apple-card--outlined:hover {
    border-color: var(--color-gray-400);
    background-color: var(--color-gray-50);
  }

  .apple-card--interactive.apple-card--ghost:hover {
    background-color: var(--color-gray-50);
  }

  /* Link styles */
  .apple-card--link {
    text-decoration: none;
    color: inherit;
  }

  /* Active states */
  .apple-card--interactive:active {
    transform: translateY(0);
  }

  .apple-card--interactive.apple-card--default:active {
    box-shadow: var(--shadow-sm);
  }

  .apple-card--interactive.apple-card--elevated:active {
    box-shadow: var(--shadow-lg);
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .apple-card--padding-lg {
      padding: var(--space-6);
    }

    .apple-card--padding-xl {
      padding: var(--space-8);
    }
  }
</style>
