<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  import { fade, scale } from 'svelte/transition';

  export let open = false;
  export let size: 'sm' | 'md' | 'lg' | 'xl' | 'full' = 'md';
  export let closeOnEscape = true;
  export let closeOnOutsideClick = true;
  export let showCloseButton = true;

  const dispatch = createEventDispatcher<{
    close: void;
    open: void;
  }>();

  let modalElement: HTMLDivElement;
  let previouslyFocused: HTMLElement | null = null;

  $: if (open) {
    dispatch('open');
    previouslyFocused = document.activeElement as HTMLElement;
    document.body.style.overflow = 'hidden';
  } else {
    dispatch('close');
    document.body.style.overflow = '';
    if (previouslyFocused) {
      previouslyFocused.focus();
    }
  }

  $: modalClass = [
    'apple-modal',
    `apple-modal--${size}`
  ].filter(Boolean).join(' ');

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape' && closeOnEscape) {
      close();
    }
  }

  function handleBackdropClick(event: MouseEvent) {
    if (closeOnOutsideClick && event.target === event.currentTarget) {
      close();
    }
  }

  function close() {
    open = false;
  }

  function trapFocus(event: KeyboardEvent) {
    if (event.key !== 'Tab') return;

    const focusableElements = modalElement.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        lastElement.focus();
        event.preventDefault();
      }
    } else {
      if (document.activeElement === lastElement) {
        firstElement.focus();
        event.preventDefault();
      }
    }
  }

  onMount(() => {
    return () => {
      document.body.style.overflow = '';
    };
  });
</script>

{#if open}
  <!-- Backdrop -->
  <div
    class="apple-modal-backdrop"
    on:click={handleBackdropClick}
    on:keydown={handleKeydown}
    transition:fade={{ duration: 200 }}
    role="dialog"
    aria-modal="true"
    tabindex="-1"
  >
    <!-- Modal -->
    <div
      bind:this={modalElement}
      class={modalClass}
      on:keydown={trapFocus}
      transition:scale={{ duration: 200, start: 0.95 }}
    >
      {#if showCloseButton}
        <button
          class="apple-modal-close"
          on:click={close}
          aria-label="Close modal"
          type="button"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      {/if}

      <slot />
    </div>
  </div>
{/if}

<style>
  .apple-modal-backdrop {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4);
    z-index: var(--z-modal-backdrop);
    overflow-y: auto;
  }

  .apple-modal {
    position: relative;
    background-color: var(--color-white);
    box-shadow: var(--shadow-xl);
    max-height: calc(100vh - var(--space-8));
    overflow-y: auto;
    z-index: var(--z-modal);
    
    /* Zero border radius for Apple flat design */
    border-radius: var(--radius-none);
    
    /* Focus styles */
    &:focus {
      outline: none;
    }
  }

  /* Size variants */
  .apple-modal--sm {
    width: 100%;
    max-width: 400px;
  }

  .apple-modal--md {
    width: 100%;
    max-width: 500px;
  }

  .apple-modal--lg {
    width: 100%;
    max-width: 700px;
  }

  .apple-modal--xl {
    width: 100%;
    max-width: 900px;
  }

  .apple-modal--full {
    width: calc(100vw - var(--space-8));
    height: calc(100vh - var(--space-8));
    max-width: none;
    max-height: none;
  }

  .apple-modal-close {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    background: none;
    border: none;
    color: var(--color-gray-500);
    cursor: pointer;
    padding: var(--space-2);
    transition: color var(--transition-fast);
    z-index: 1;
    
    /* Zero border radius */
    border-radius: var(--radius-none);
    
    &:hover {
      color: var(--color-gray-700);
    }
    
    &:focus {
      outline: 2px solid var(--color-blue-500);
      outline-offset: 2px;
      color: var(--color-gray-700);
    }
    
    &:focus:not(:focus-visible) {
      outline: none;
    }
  }

  .apple-modal-close svg {
    width: 20px;
    height: 20px;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .apple-modal-backdrop {
      padding: var(--space-2);
    }

    .apple-modal--sm,
    .apple-modal--md,
    .apple-modal--lg,
    .apple-modal--xl {
      width: 100%;
      max-width: none;
      margin: 0;
    }

    .apple-modal--full {
      width: 100vw;
      height: 100vh;
      max-height: 100vh;
    }

    .apple-modal-backdrop {
      padding: 0;
    }
  }

  /* Animation improvements */
  @media (prefers-reduced-motion: reduce) {
    .apple-modal-backdrop {
      transition: none;
    }
    
    .apple-modal {
      transition: none;
    }
  }
</style>
