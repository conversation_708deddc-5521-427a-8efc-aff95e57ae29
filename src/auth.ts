import {
	AUTH_GOOGLE_ID,
	AUTH_GOOGLE_SECRET
} from '$env/static/private';
import { find_or_create_user } from '$lib/auth';
import { SvelteKitAuth } from '@auth/sveltekit';
// Custom Google provider using <PERSON><PERSON>'s fetch API
interface GoogleProfile {
  sub: string;
  name: string;
  email: string;
  picture: string;
}

const GoogleProvider = {
  id: 'google' as const,
  name: 'Google',
  type: 'oidc' as const,
  issuer: 'https://accounts.google.com',
  authorization: { params: { scope: 'openid email profile' } },
  idToken: true,
  clientId: AUTH_GOOGLE_ID,
  clientSecret: AUTH_GOOGLE_SECRET,
  profile(profile: GoogleProfile) {
    return {
      id: profile.sub,
      name: profile.name,
      email: profile.email,
      image: profile.picture
    };
  }
};

export const { handle, signIn, signOut } = SvelteKitAuth({
	providers: [GoogleProvider],
	callbacks: {
		jwt: async ({ token, user, account }) => {
			return token; // Return the token as is for now
/* 			if (user) {
				try {
					token.id = (await find_or_create_user({
						id: user.id!,
						name: user.name!,
						email: user.email!
					}))!.id;
					console.log('find_or_create_user success:', token.id);
				} catch (error) {
					console.error('find_or_create_user error:', error);
				}
			}
			if (account) {
				return {
					...token,
					access_token: account.access_token,
					expires_at: account.expires_at,
					refresh_token: account.refresh_token
				};
			} else if (Date.now() < token.expires_at * 1000) {
				return token;
			} else {
				console.log('getting new access token');
				if (!token.refresh_token) {
					console.error('Missing refresh_token');
					return token;
				}

				try {
					const response = await fetch(
						'https://oauth2.googleapis.com/token',
						{
							method: 'POST',
							body: new URLSearchParams({
								client_id: AUTH_GOOGLE_ID!,
								client_secret: AUTH_GOOGLE_SECRET!,
								grant_type: 'refresh_token',
								refresh_token: token.refresh_token!
							})
						}
					);

					const tokensOrError = await response.json();

					if (!response.ok) {
						console.error('jwt callback tokens error', tokensOrError);
						return token;
					}
					const newTokens = tokensOrError as {
						access_token: string;
						expires_in: number;
						refresh_token?: string;
					};
					return {
						...token,
						access_token: newTokens.access_token,
						expires_at: Math.floor(
							Date.now() / 1000 + newTokens.expires_in
						),
						// Some providers only issue refresh tokens once, so preserve if we did not get a new one
						refresh_token: newTokens.refresh_token
							? newTokens.refresh_token
							: token.refresh_token
					};
				} catch (error) {
					console.error('Error refreshing access_token', error);
					// If we fail to refresh the token, return an error so we can handle it on the page
					token.error = 'RefreshTokenError';
					return token;
				}
			} */
		},
		session({ session, token }) {
			// You can customize the session object here
			if (token.error) {
				(session as any).error = token.error;
			}
			(session.user as any).id = token.id; // Add id to user object
			return session;
		}}
});
